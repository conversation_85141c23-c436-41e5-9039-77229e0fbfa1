/* 采购订单行价格同步按钮样式 */

/* 禁用状态的按钮样式（当价格表中已存在相同数量和价格的记录时） */
button[name="action_sync_price_to_pricelist"]:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

button[name="action_sync_price_to_pricelist"]:disabled i {
    color: #cccccc !important;
}

/* 启用状态的按钮样式 */
button[name="action_sync_price_to_pricelist"]:not(:disabled) {
    opacity: 1;
    cursor: pointer;
}

button[name="action_sync_price_to_pricelist"]:not(:disabled) i {
    color: #28a745 !important;
}

/* 悬停效果 */
button[name="action_sync_price_to_pricelist"]:not(:disabled):hover i {
    color: #007bff !important;
}

/* 行装饰样式 - 当按钮禁用时使行变灰 */
.o_data_row.text-muted {
    opacity: 0.7;
}
