<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 扩展采购订单表单视图，添加价格表相关按钮 -->
    <record id="purchase_order_form_inherit" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//header/button[@name='action_rfq_send']" position="after">
                <button name="action_generate_supplierinfo" 
                        string="更新到供应商价格表"
                        icon="fa-arrow-up" 
                        type="object" 
                        class="btn-primary" 
                        invisible="state not in ['draft', 'sent', 'purchase']"/>
                <button name="action_update_order_line" 
                        string="应用价格"
                        icon="fa-arrow-down" 
                        type="object" 
                        class="btn-primary" 
                        invisible="state not in ['draft', 'sent', 'purchase']"/>
            </xpath>
            
            <!-- 添加采购订单行中的同步按钮和相关字段 -->
            <xpath expr="//field[@name='order_line']/list" position="attributes">
                <attribute name="decoration-muted">show_price_sync_btn == False</attribute>
            </xpath>
            
            <xpath expr="//field[@name='order_line']/list/field[@name='price_unit']" position="after">
                <field name="show_price_sync_btn" column_invisible="1"/>
                <button name="action_sync_price_to_pricelist"
                        type="object"
                        icon="fa-refresh"
                        title="同步价格到供应商价格表"
                        class="btn btn-sm o_button_icon p-1"
                        disabled="not show_price_sync_btn"
                        context="{'skip_save': 1, 'skip_save_fields': ['price_unit', 'order_id', 'product_id', 'product_qty']}" />
            </xpath>
        </field>
    </record>
</odoo> 